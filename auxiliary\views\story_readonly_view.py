from typing import Any, Dict, List, Union

import discord
from discord.ext import commands

from auxiliary.services.story.constants import EMBED_DESCRIPTION_LIMIT, UIComponentID
from auxiliary.services.story.story_logic import THEMES_BY_TITLE
from gacha.views.collection.collection_view.base_pagination import BasePaginationView

BotType = Union[commands.Bot, commands.AutoShardedBot]


def split_text_nicely(text: str, max_length: int) -> List[str]:
    """
    將長文本分割成多個部分，盡量在自然的斷句點（如換行符）分割。
    """
    if len(text) <= max_length:
        return [text]

    parts = []
    while len(text) > max_length:
        split_pos = text.rfind("\n", 0, max_length)
        if split_pos == -1:
            split_pos = max_length
        parts.append(text[:split_pos])
        text = text[split_pos:].lstrip()
    parts.append(text)
    return parts


class StoryReadOnlyView(BasePaginationView):
    """用於查看分享故事的只讀視圖"""

    def __init__(
        self,
        bot: BotType,
        user: Union[discord.User, discord.Member],
        story_data: Dict[str, Any],
    ):
        self.story_data = story_data
        self.story_meta = story_data["story"]
        self.turns = story_data["turns"]
        self.chunk_summaries = story_data.get("chunk_summaries", [])
        
        # 模式狀態：True=查看總結，False=查看故事
        self.summary_mode = False
        
        # 計算分頁
        story_pages = (len(self.turns) + 1) // 2 if self.turns else 1
        summary_pages = len(self.chunk_summaries) if self.chunk_summaries else 1
        total_pages = summary_pages if self.summary_mode else story_pages

        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=1,
            total_pages=total_pages,
            timeout=None,
        )
        self.current_sub_page = 1
        self.total_sub_pages = 1

    async def start(self) -> discord.Embed:
        """產生視圖的初始 Embed。"""
        embed = await self.get_current_page_embed()
        self._add_mode_toggle_button()
        return embed
    
    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed - BasePaginationView要求實現"""
        if self.summary_mode:
            return self._create_summary_embed()
        else:
            return self._create_story_embed()
    
    def _switch_mode(self):
        """切換查看模式並重新計算分頁"""
        self.summary_mode = not self.summary_mode
        
        # 重新計算總頁數
        if self.summary_mode:
            self.total_pages = len(self.chunk_summaries) if self.chunk_summaries else 1
        else:
            self.total_pages = (len(self.turns) + 1) // 2 if self.turns else 1
        
        # 重置到第一頁
        self.current_page = 1
        self.current_sub_page = 1

    def _add_buttons(self):
        """集中管理按鈕的添加。"""
        self.clear_items()
        self.add_pagination_buttons(row=0)
        self._add_sub_pagination_buttons()  # 故事和總結模式都可能需要子分頁
        self._add_mode_toggle_button()
    
    def _add_mode_toggle_button(self):
        """添加模式切換按鈕"""
        if not self.chunk_summaries:  # 沒有總結時不顯示切換按鈕
            return
        
        if self.summary_mode:
            button_label = "📖 查看故事"
            button_style = discord.ButtonStyle.primary
        else:
            button_label = "📝 查看總結"
            button_style = discord.ButtonStyle.secondary
        
        toggle_button = discord.ui.Button(
            label=button_label,
            style=button_style,
            row=2,
        )
        toggle_button.callback = self.toggle_mode_callback
        self.add_item(toggle_button)

    def _create_story_embed(self) -> discord.Embed:
        """根據當前狀態建立並返回故事 Embed，使用智慧分頁佈局。"""
        page = self.current_page
        turn_index = (page - 1) * 2

        if not self.turns or turn_index >= len(self.turns):
            return discord.Embed(
                title="😕 錯誤",
                description="找不到這個回合的資料。",
                color=discord.Color.red(),
            )

        ai_turn = self.turns[turn_index]
        user_turn = (
            self.turns[turn_index + 1] if turn_index + 1 < len(self.turns) else None
        )

        full_content = ai_turn.get("content", "")
        status_block_content = ai_turn.get("status_block", "")

        pages_data = self._plan_content_layout(
            full_content, status_block_content, user_turn
        )

        self.total_sub_pages = len(pages_data)
        if self.current_sub_page > self.total_sub_pages:
            self.current_sub_page = 1

        current_page_data = (
            pages_data[self.current_sub_page - 1]
            if pages_data
            else {"description": "", "field": None}
        )

        story_title = self.story_meta.get("title", "無標題")
        embed = discord.Embed(
            title=f"📖 {story_title} - 分享故事",
            description=current_page_data["description"],
            color=discord.Color.from_rgb(114, 137, 218),
        )

        if current_page_data["field"]:
            embed.add_field(
                name="📊 角色狀態 📊", value=current_page_data["field"], inline=False
            )

        theme_title = self.story_meta.get("theme_title")
        if theme_title:
            theme_data = THEMES_BY_TITLE.get(theme_title)
            if theme_data and theme_data.get("image_url"):
                embed.set_thumbnail(url=theme_data["image_url"])

        footer_text = (
            f"第 {page}/{self.total_pages} 頁 | 分享ID: {self.story_meta['id']}"
        )
        if self.total_sub_pages > 1:
            footer_text += f" (內容 {self.current_sub_page}/{self.total_sub_pages})"

        embed.set_footer(text=footer_text)
        return embed

    def _plan_content_layout(
        self,
        main_content: str,
        status_content: str,
        user_turn: Dict[str, Any] | None,
    ) -> List[Dict[str, Any]]:
        user_choice_text = ""
        if user_turn:
            user_choice_text = f"\n\n**🤔 玩家的選擇**\n`{user_turn['content']}`"

        pages = []

        if not status_content or len(status_content) <= 1024:
            base_description = f"**📖 故事情節**\n{main_content}{user_choice_text}"
            split_descriptions = split_text_nicely(
                base_description, EMBED_DESCRIPTION_LIMIT
            )

            for i, desc in enumerate(split_descriptions):
                is_last_sub_page = i == len(split_descriptions) - 1
                field_content = (
                    status_content if (status_content and is_last_sub_page) else None
                )
                pages.append({"description": desc, "field": field_content})

        else:
            combined_content = f"**📖 故事情節**\n{main_content}{user_choice_text}\n\n📊 角色狀態 📊\n{status_content}"
            split_combined = split_text_nicely(
                combined_content, EMBED_DESCRIPTION_LIMIT
            )

            for combined_part in split_combined:
                pages.append({"description": combined_part, "field": None})

        return (
            pages
            if pages
            else [{"description": "**📖 故事情節**\n（暫無內容）", "field": None}]
        )

    def _add_sub_pagination_buttons(self):
        if self.total_sub_pages <= 1:
            return

        prev_button = discord.ui.Button(
            label="◀️ 上一頁內容",
            style=discord.ButtonStyle.secondary,
            custom_id=UIComponentID.SUB_PREV,
            row=1,
            disabled=(self.current_sub_page == 1),
        )
        prev_button.callback = self.sub_page_previous_callback
        self.add_item(prev_button)

        next_button = discord.ui.Button(
            label="下一頁內容 ▶️",
            style=discord.ButtonStyle.secondary,
            custom_id=UIComponentID.SUB_NEXT,
            row=1,
            disabled=(self.current_sub_page == self.total_sub_pages),
        )
        next_button.callback = self.sub_page_next_callback
        self.add_item(next_button)

    async def sub_page_previous_callback(self, interaction: discord.Interaction):
        if self.current_sub_page > 1:
            self.current_sub_page -= 1
        await self._redraw_message(interaction)

    async def sub_page_next_callback(self, interaction: discord.Interaction):
        if self.current_sub_page < self.total_sub_pages:
            self.current_sub_page += 1
        await self._redraw_message(interaction)

    async def _redraw_message(self, interaction: discord.Interaction):
        """根據當前狀態統一重繪消息。"""
        if not interaction.response.is_done():
            await interaction.response.defer()

        embed = await self.get_current_page_embed()
        self._add_buttons()
        self._refresh_button_states()
        await interaction.edit_original_response(embed=embed, view=self)

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面 - BasePaginationView要求實現"""
        self.current_sub_page = 1  # 重置子頁面
        embed = await self.get_current_page_embed()
        self._add_buttons()
        self._refresh_button_states()
        await interaction.response.edit_message(embed=embed, view=self)
    
    async def toggle_mode_callback(self, interaction: discord.Interaction):
        """切換查看模式的回調函數"""
        self._switch_mode()
        embed = await self.get_current_page_embed()
        self._add_buttons()
        self._refresh_button_states()
        await interaction.response.edit_message(embed=embed, view=self)
    
    def _create_summary_embed(self) -> discord.Embed:
        """創建總結模式的Embed"""
        if not self.chunk_summaries:
            return discord.Embed(
                title="📝 故事總結",
                description="此故事尚未生成任何總結。",
                color=discord.Color.orange(),
            )
        
        current_summary = self.chunk_summaries[self.current_page - 1]
        start_floor = (current_summary["start_turn_number"] + 1) // 2
        end_floor = current_summary["end_turn_number"] // 2
        
        # 智能分割總結內容以適應Discord限制
        summary_content = current_summary["content"]
        max_desc_length = 4000  # 保留一些空間給其他內容
        
        if len(summary_content) <= max_desc_length:
            display_content = summary_content
        else:
            # 分割長文本並顯示當前子頁面
            content_parts = split_text_nicely(summary_content, max_desc_length)
            self.total_sub_pages = len(content_parts)
            
            if self.current_sub_page > len(content_parts):
                self.current_sub_page = 1
                
            display_content = content_parts[self.current_sub_page - 1]
        
        embed = discord.Embed(
            title=f"📝 第{start_floor}-{end_floor}樓層總結",
            description=display_content,
            color=discord.Color.blue(),
        )
        
        story_title = self.story_meta.get("title", "無標題")
        embed.set_author(name=f"📖 {story_title} - 故事總結")
        
        footer_text = f"總結 {self.current_page}/{self.total_pages} | 分享ID: {self.story_meta['id']}"
        if hasattr(self, 'total_sub_pages') and self.total_sub_pages > 1:
            footer_text += f" (內容 {self.current_sub_page}/{self.total_sub_pages})"
        
        embed.set_footer(text=footer_text)
        
        return embed
