# summary_management_view_v2.py

import functools
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Tuple, Union
from uuid import UUID

import discord
from discord.ext import commands

from auxiliary.exceptions import AIConnectionError
from auxiliary.services import story_logic
from auxiliary.services.story.constants import (
    CHUNK_SIZE_FLOORS,
    PROTECTED_RECENT_FLOORS,
    SUMMARY_PAGE_SIZE,
)
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from utils.text_utils import split_text_nicely

if TYPE_CHECKING:
    from auxiliary.cogs.story_cog import StoryCog


class SummaryManagementView(BasePaginationView):
    """
    獨立的總結管理視圖類
    
    這個類專門負責處理故事的總結管理功能，包括：
    - 顯示可總結的區塊
    - 生成/重新生成總結
    - 查看總結內容
    - 分頁顯示總結內容
    
    設計原則：
    - 不再繼承 BasePaginationView（不需要分頁功能）
    - 使用回調函數機制而非直接引用避免循環引用
    - 清晰的職責分離
    """

    def __init__(
        self,
        bot: Union[commands.Bot, commands.AutoShardedBot],
        user: Union[discord.User, discord.Member],
        story_cog: "StoryCog",
        story_id: Union[str, UUID],
        story_meta: Dict[str, Any],
        total_turn_count: int,
        back_callback: Optional[Callable[[discord.Interaction], Any]] = None,
    ):
        self.story_cog = story_cog
        self.story_id = str(story_id) if isinstance(story_id, UUID) else story_id
        self.story_meta = story_meta
        self.total_turn_count = total_turn_count
        self.back_callback = back_callback
        
        # 計算區塊信息
        chunks = self._calculate_chunks()
        total_chunk_pages = len(chunks) if chunks else 1
        
        # 初始化BasePaginationView
        super().__init__(
            bot=bot, 
            user_id=user.id, 
            current_page=1, 
            total_pages=total_chunk_pages,
            timeout=None
        )
        
        # 緩存管理
        self._cached_chunk_summaries = None
        self._cache_timestamp = None
        
        # 當前區塊內容分頁狀態
        self.current_chunk_content_page = 1
        self.total_chunk_content_pages = 1

    def _calculate_chunks(self) -> List[Dict[str, Any]]:
        """計算所有可總結的區塊信息。"""
        total_floors = self.total_turn_count // 2
        chunks = []

        for chunk_start in range(1, total_floors, CHUNK_SIZE_FLOORS):
            chunk_end = min(chunk_start + CHUNK_SIZE_FLOORS - 1, total_floors)
            turn_start = (chunk_start - 1) * 2 + 1
            turn_end = chunk_end * 2

            # 只有不是最新的區塊才可以總結
            if chunk_end <= total_floors - PROTECTED_RECENT_FLOORS:
                chunk_num = (chunk_start - 1) // CHUNK_SIZE_FLOORS + 1
                chunks.append(
                    {
                        "chunk_num": chunk_num,
                        "floors": f"{chunk_start}-{chunk_end}",
                        "turns": f"{turn_start}-{turn_end}",
                        "start_turn": turn_start,
                        "end_turn": turn_end,
                    }
                )

        return chunks

    async def _get_chunk_summaries_with_cache(self) -> Dict[tuple, Dict[str, Any]]:
        """獲取區塊總結，使用緩存優化性能。"""
        import time

        current_time = time.time()

        # 如果緩存存在且在5分鐘內，使用緩存
        if (
            self._cached_chunk_summaries is not None
            and self._cache_timestamp is not None
            and current_time - self._cache_timestamp < 300
        ):
            return self._cached_chunk_summaries

        # 重新獲取數據
        try:
            chunk_summaries = (
                await story_logic.story_repository.get_chunk_summaries_for_story(
                    UUID(self.story_id)
                )
            )
            existing_summaries = {
                (s["start_turn_number"], s["end_turn_number"]): s for s in chunk_summaries
            }

            # 更新緩存
            self._cached_chunk_summaries = existing_summaries
            self._cache_timestamp = current_time

            return existing_summaries
        except Exception:
            # 如果獲取失敗，返回空字典而不是崩潰
            return {}

    def _invalidate_summary_cache(self):
        """使緩存失效（當總結被修改時調用）。"""
        self._cached_chunk_summaries = None
        self._cache_timestamp = None

    async def start(self) -> discord.Embed:
        """
        啟動總結管理視圖，返回初始嵌入
        """
        chunks = self._calculate_chunks()
        
        
        # 添加額外的功能按鈕（內容分頁和功能按鈕）
        await self._add_extra_buttons()
        return await self.get_current_page_embed()
    
    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed - BasePaginationView要求實現"""
        return await self._create_chunk_display_embed()
    
    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面 - BasePaginationView要求實現"""
        # 重置內容頁到第一頁
        self.current_chunk_content_page = 1
        
        # 完全重建界面
        await self._rebuild_interface()
        
        # 獲取新的embed
        embed = await self.get_current_page_embed()
        
        # 更新消息
        await interaction.response.edit_message(embed=embed, view=self)

    async def _create_chunk_display_embed(self) -> discord.Embed:
        """創建區塊顯示模式的嵌入。"""
        chunks = self._calculate_chunks()
        existing_summaries = await self._get_chunk_summaries_with_cache()
        
        if not chunks:
            return discord.Embed(
                title="🗂️ 長期記憶總結管理",
                description="當前故事長度還不足以進行區塊總結。\n需要達到50樓層後才能使用此功能。",
                color=discord.Color.orange(),
            )
        
        # 獲取當前頁的區塊（使用BasePaginationView的current_page）
        current_chunk = chunks[self.current_page - 1]
        chunk_key = (current_chunk["start_turn"], current_chunk["end_turn"])
        has_summary = chunk_key in existing_summaries
        
        # 設置embed標題和顏色
        if has_summary:
            title = f"📄 區塊 {current_chunk['chunk_num']} - 樓層 {current_chunk['floors']}"
            color = discord.Color.green()
            status_icon = "✅"
        else:
            title = f"📝 區塊 {current_chunk['chunk_num']} - 樓層 {current_chunk['floors']}"
            color = discord.Color.orange()
            status_icon = "🔄"
        
        embed = discord.Embed(title=title, color=color)
        
        if has_summary:
            # 顯示總結內容
            summary_data = existing_summaries[chunk_key]
            content = summary_data["content"]
            
            # 分頁顯示總結內容
            content_chunks = split_text_nicely(content, SUMMARY_PAGE_SIZE)
            self.total_chunk_content_pages = len(content_chunks)
            
            # 確保當前頁數不超過總頁數
            if self.current_chunk_content_page > self.total_chunk_content_pages:
                self.current_chunk_content_page = 1
            
            current_content = content_chunks[self.current_chunk_content_page - 1]
            embed.description = current_content
            
            # 總是顯示頁數信息
            if self.total_chunk_content_pages > 1:
                embed.set_footer(text=f"內容頁面 {self.current_chunk_content_page}/{self.total_chunk_content_pages} | 區塊 {self.current_page}/{self.total_pages}")
            else:
                embed.set_footer(text=f"區塊 {self.current_page}/{self.total_pages}")
            
        else:
            embed.description = "此區塊尚未生成總結。\n點擊下方按鈕生成AI總結以鞏固記憶。"
            embed.set_footer(text=f"區塊 {self.current_page}/{self.total_pages}")
            self.total_chunk_content_pages = 1
        
        return embed

    async def _add_extra_buttons(self):
        """添加額外的功能按鈕。"""
        chunks = self._calculate_chunks()
        if not chunks:
            self._add_back_button_only()
            return
        
        current_chunk, has_summary = await self._get_current_chunk_info()
        
        # 添加各種按鈕
        await self._add_content_pagination_buttons(has_summary)
        self._add_action_buttons(current_chunk, has_summary)
        self._add_utility_buttons()
    
    def _add_back_button_only(self):
        """只添加返回按鈕。"""
        back_button = discord.ui.Button(
            label="⬅️ 返回故事",
            style=discord.ButtonStyle.secondary,
            row=1,
        )
        back_button.callback = self.back_to_story_callback
        self.add_item(back_button)
    
    async def _get_current_chunk_info(self):
        """獲取當前區塊信息。"""
        chunks = self._calculate_chunks()
        existing_summaries = await self._get_chunk_summaries_with_cache()
        current_chunk = chunks[self.current_page - 1]
        chunk_key = (current_chunk["start_turn"], current_chunk["end_turn"])
        has_summary = chunk_key in existing_summaries
        return current_chunk, has_summary
    
    async def _add_content_pagination_buttons(self, has_summary: bool):
        """添加內容分頁按鈕。"""
        if not has_summary:
            return
        
        content_pages = await self._get_content_pages_count()
        if content_pages <= 1:
            return
        
        if self.current_chunk_content_page > 1:
            prev_button = discord.ui.Button(
                label="◀️ 上一頁",
                style=discord.ButtonStyle.primary,
                row=1,
            )
            prev_button.callback = self.prev_content_page_callback
            self.add_item(prev_button)
        
        if self.current_chunk_content_page < content_pages:
            next_button = discord.ui.Button(
                label="▶️ 下一頁",
                style=discord.ButtonStyle.primary,
                row=1,
            )
            next_button.callback = self.next_content_page_callback
            self.add_item(next_button)
    
    def _add_action_buttons(self, current_chunk: Dict[str, Any], has_summary: bool):
        """添加主要功能按鈕。"""
        if has_summary:
            button = discord.ui.Button(
                label="🔄 重新生成總結",
                style=discord.ButtonStyle.danger,
                row=2,
            )
            button.callback = functools.partial(
                self._generate_chunk_summary,
                chunk_num=current_chunk["chunk_num"],
                start_turn=current_chunk["start_turn"],
                end_turn=current_chunk["end_turn"],
                is_regenerate=True
            )
        else:
            button = discord.ui.Button(
                label="✍️ 生成總結",
                style=discord.ButtonStyle.primary,
                row=2,
            )
            button.callback = functools.partial(
                self._generate_chunk_summary,
                chunk_num=current_chunk["chunk_num"],
                start_turn=current_chunk["start_turn"],
                end_turn=current_chunk["end_turn"],
                is_regenerate=False
            )
        self.add_item(button)
    
    def _add_utility_buttons(self):
        """添加工具按鈕。"""
        help_button = discord.ui.Button(
            label="❓ 功能說明",
            style=discord.ButtonStyle.secondary,
            row=3,
        )
        help_button.callback = self.show_help_callback
        self.add_item(help_button)
        
        back_button = discord.ui.Button(
            label="⬅️ 返回故事",
            style=discord.ButtonStyle.secondary,
            row=3,
        )
        back_button.callback = self.back_to_story_callback
        self.add_item(back_button)
    
    async def _get_content_pages_count(self) -> int:
        """獲取內容分頁數量。"""
        chunks = self._calculate_chunks()
        existing_summaries = await self._get_chunk_summaries_with_cache()
        current_chunk = chunks[self.current_page - 1]
        chunk_key = (current_chunk["start_turn"], current_chunk["end_turn"])
        
        if chunk_key not in existing_summaries:
            return 1
        
        summary_data = existing_summaries[chunk_key]
        content = summary_data["content"]
        content_chunks = split_text_nicely(content, SUMMARY_PAGE_SIZE)
        return len(content_chunks)

    async def _show_loading_embed(
        self, interaction: discord.Interaction, title: str, description: str
    ):
        """顯示統一的載入界面。"""
        # 禁用所有按鈕和選單（參考StoryMasterView的做法）
        for item in self.children:
            if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                item.disabled = True
        
        await interaction.response.edit_message(
            embed=discord.Embed(
                title=f"<a:Loading:1392930453219967149> {title}",
                description=description,
                color=discord.Color.orange(),
            ),
            view=self,
        )

    async def _show_error_embed(
        self, interaction: discord.Interaction, title: str, description: str
    ):
        """顯示統一的錯誤界面。"""
        # 重新啟用所有按鈕和選單
        for item in self.children:
            if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                item.disabled = False
        
        await interaction.edit_original_response(
            embed=discord.Embed(
                title=f"❌ {title}",
                description=description,
                color=discord.Color.red(),
            ),
            view=self,
        )

    async def _rebuild_interface(self):
        """重建界面（不處理interaction）。"""
        self.clear_items()
        self.add_pagination_buttons()
        self._refresh_button_states()
        await self._add_extra_buttons()
    
    async def _reload_interface_with_response(
        self, interaction: discord.Interaction, success_message: Optional[str] = None
    ):
        """重載界面並發送響應。"""
        self._invalidate_summary_cache()
        await self._rebuild_interface()
        embed = await self.get_current_page_embed()

        if success_message:
            embed.color = discord.Color.green()
            embed.add_field(name="✅ 操作完成", value=success_message, inline=False)

        await interaction.response.edit_message(embed=embed, view=self)
    
    async def _reload_interface_with_edit(
        self, interaction: discord.Interaction, success_message: Optional[str] = None
    ):
        """重載界面並編輯原始訊息。"""
        self._invalidate_summary_cache()
        await self._rebuild_interface()
        embed = await self.get_current_page_embed()

        if success_message:
            embed.color = discord.Color.green()
            embed.add_field(name="✅ 操作完成", value=success_message, inline=False)

        await interaction.edit_original_response(embed=embed, view=self)

    async def _generate_chunk_summary(
        self,
        interaction: discord.Interaction,
        *,
        chunk_num: int,
        start_turn: int,
        end_turn: int,
        is_regenerate: bool = False,
    ):
        """統一的區塊總結生成方法。"""
        user_id = interaction.user.id
        action = "重新生成" if is_regenerate else "生成"

        # 檢查用戶是否正在處理其他故事操作
        if user_id in self.story_cog.active_story_users:
            await interaction.response.send_message(
                "你的故事正在生成中，請稍候再試。", ephemeral=True
            )
            return

        # 將用戶加入處理中集合
        self.story_cog.active_story_users.add(user_id)
        
        try:
            # 禁用所有按鈕並顯示載入界面
            await self._show_loading_embed(
                interaction,
                f"正在{action}總結...",
                f"AI 正在{'重新' if is_regenerate else ''}分析區塊{chunk_num}的內容並生成總結，請稍候...",
            )

            # 如果是重新生成，先刪除舊總結
            if is_regenerate:
                await story_logic.story_repository.delete_chunk_summary_by_range(
                    UUID(self.story_id), start_turn, end_turn
                )

            # 生成總結
            success = await story_logic.generate_chunk_summary(
                user_id=self.user_id,
                story_id=UUID(self.story_id),
                start_turn_number=start_turn,
                end_turn_number=end_turn,
            )

            if success:
                success_message = f"已成功{action}區塊{chunk_num}的總結！"
                await self._reload_interface_with_edit(interaction, success_message)
            else:
                await self._show_error_embed(
                    interaction,
                    f"{action}失敗",
                    f"{action}總結時發生錯誤，請稍後再試。",
                )

        except AIConnectionError:
            await self._show_error_embed(
                interaction, "AI 連接錯誤", "無法連接到 AI 服務，請稍後再試。"
            )
        except Exception as e:
            await self._show_error_embed(
                interaction, "發生錯誤", f"{action}總結時發生意外錯誤: {str(e)}"
            )
        finally:
            # 確保移除用戶處理狀態
            self.story_cog.active_story_users.discard(user_id)


    async def prev_content_page_callback(self, interaction: discord.Interaction):
        """上一頁內容的回調函數。"""
        if self.current_chunk_content_page > 1:
            self.current_chunk_content_page -= 1
            await self._reload_interface_with_response(interaction)
        else:
            await interaction.response.defer()

    async def next_content_page_callback(self, interaction: discord.Interaction):
        """下一頁內容的回調函數。"""
        if self.current_chunk_content_page < self.total_chunk_content_pages:
            self.current_chunk_content_page += 1
            await self._reload_interface_with_response(interaction)
        else:
            await interaction.response.defer()

    async def show_help_callback(self, interaction: discord.Interaction):
        """顯示功能說明的回調函數。"""
        help_embed = discord.Embed(
            title="🗂️ 總結功能說明",
            description="長期記憶總結管理功能詳細說明",
            color=discord.Color.blue(),
        )
        
        help_embed.add_field(
            name="📖 功能用途",
            value="• 鞏固AI記憶，防止記憶錯亂\n• 每40樓層可合併為一個「大總結」\n• 最近10樓層會保持完整記憶",
            inline=False,
        )
        
        help_embed.add_field(
            name="🎯 使用時機",
            value="• 故事達到50樓層後可開始使用\n• 建議定期生成總結以維護記憶品質",
            inline=False,
        )
        
        help_embed.add_field(
            name="🎮 操作說明",
            value="• 使用左右箭頭切換不同區塊\n• 已有總結會直接顯示內容\n• 未有總結可點擊生成按鈕",
            inline=False,
        )
        
        await interaction.response.send_message(embed=help_embed, ephemeral=True)

    async def back_to_story_callback(self, interaction: discord.Interaction):
        """返回故事模式的回調函數。"""
        if self.back_callback:
            # 使用回調函數而非直接引用，避免循環引用
            await self.back_callback(interaction)
        else:
            # 如果沒有回調函數，顯示錯誤信息
            await interaction.response.send_message(
                "無法返回故事界面，請重新啟動。", ephemeral=True
            )